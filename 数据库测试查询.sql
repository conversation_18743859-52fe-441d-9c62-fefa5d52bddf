-- 社区便民服务管理系统数据库测试查询
-- 使用此文件验证数据库创建和数据插入是否成功

USE ccsm_system;

-- 1. 验证所有表是否创建成功
SHOW TABLES;

-- 2. 验证管理员数据
SELECT '=== 管理员数据 ===' AS info;
SELECT id, username, phone, real_name, status, created_time FROM admins;

-- 3. 验证用户数据
SELECT '=== 用户数据 ===' AS info;
SELECT id, username, real_name, phone, volunteer_hours, status FROM users;

-- 4. 验证公告数据
SELECT '=== 公告数据 ===' AS info;
SELECT id, title, status, view_count, admin_id FROM announcements;

-- 5. 验证活动数据
SELECT '=== 活动数据 ===' AS info;
SELECT id, title, location, max_participants, current_participants, 
       activity_start_time, registration_end_time, status FROM activities;

-- 6. 验证志愿服务数据
SELECT '=== 志愿服务数据 ===' AS info;
SELECT id, title, location, max_participants, current_participants, 
       service_hours, service_start_time, status FROM volunteer_services;

-- 7. 验证活动报名数据
SELECT '=== 活动报名统计 ===' AS info;
SELECT a.title AS activity_title, COUNT(ar.id) AS registration_count
FROM activities a 
LEFT JOIN activity_registrations ar ON a.id = ar.activity_id 
WHERE ar.status IN (1,2)
GROUP BY a.id, a.title;

-- 8. 验证志愿服务报名数据
SELECT '=== 志愿服务报名统计 ===' AS info;
SELECT vs.title AS service_title, COUNT(vr.id) AS registration_count
FROM volunteer_services vs 
LEFT JOIN volunteer_registrations vr ON vs.id = vr.service_id 
WHERE vr.status IN (1,2,3)
GROUP BY vs.id, vs.title;

-- 9. 验证用户志愿时长统计
SELECT '=== 用户志愿时长统计 ===' AS info;
SELECT u.real_name, u.volunteer_hours, 
       COUNT(vr.id) AS confirmed_services
FROM users u 
LEFT JOIN volunteer_registrations vr ON u.id = vr.user_id AND vr.status = 3
GROUP BY u.id, u.real_name, u.volunteer_hours
ORDER BY u.volunteer_hours DESC;

-- 10. 验证系统统计数据
SELECT '=== 系统统计数据 ===' AS info;
SELECT * FROM system_statistics ORDER BY stat_date;

-- 11. 功能测试查询示例

-- 查询用户的所有报名记录（活动+志愿服务）
SELECT '=== 用户张三的报名记录 ===' AS info;
SELECT 'activity' AS type, a.title, ar.status, ar.registration_time
FROM activity_registrations ar
JOIN activities a ON ar.activity_id = a.id
JOIN users u ON ar.user_id = u.id
WHERE u.username = 'zhangsan'
UNION ALL
SELECT 'volunteer' AS type, vs.title, vr.status, vr.registration_time
FROM volunteer_registrations vr
JOIN volunteer_services vs ON vr.service_id = vs.id
JOIN users u ON vr.user_id = u.id
WHERE u.username = 'zhangsan'
ORDER BY registration_time DESC;

-- 查询当前可报名的活动
SELECT '=== 当前可报名的活动 ===' AS info;
SELECT title, location, max_participants, current_participants,
       activity_start_time, registration_end_time
FROM activities 
WHERE status = 1 
  AND registration_start_time <= NOW() 
  AND registration_end_time >= NOW()
  AND current_participants < max_participants;

-- 查询当前可报名的志愿服务
SELECT '=== 当前可报名的志愿服务 ===' AS info;
SELECT title, location, max_participants, current_participants,
       service_hours, service_start_time, registration_end_time
FROM volunteer_services 
WHERE status = 1 
  AND registration_start_time <= NOW() 
  AND registration_end_time >= NOW()
  AND current_participants < max_participants;

-- 查询需要确认的志愿服务
SELECT '=== 需要管理员确认的志愿服务 ===' AS info;
SELECT vs.title, vs.status, COUNT(vr.id) AS pending_confirmations
FROM volunteer_services vs
JOIN volunteer_registrations vr ON vs.id = vr.service_id
WHERE vs.status = 3 AND vr.status = 2
GROUP BY vs.id, vs.title, vs.status;

-- 验证数据完整性
SELECT '=== 数据完整性检查 ===' AS info;
SELECT 
  (SELECT COUNT(*) FROM users) AS total_users,
  (SELECT COUNT(*) FROM admins) AS total_admins,
  (SELECT COUNT(*) FROM activities) AS total_activities,
  (SELECT COUNT(*) FROM volunteer_services) AS total_services,
  (SELECT COUNT(*) FROM announcements) AS total_announcements,
  (SELECT COUNT(*) FROM activity_registrations) AS total_activity_registrations,
  (SELECT COUNT(*) FROM volunteer_registrations) AS total_volunteer_registrations;

SELECT '=== 数据库测试完成 ===' AS info;
