# 社区便民服务管理系统数据库设计说明

## 项目概述
- **项目名称**: 社区便民服务管理系统 (CCSM System)
- **技术栈**: SpringBoot + Vue3 + MySQL + MyBatis Plus
- **数据库**: ccsm_system
- **字符集**: utf8mb4_unicode_ci

## 数据库表结构设计

### 1. 用户表 (users)
**功能**: 存储社区居民用户信息
- `id`: 主键，用户唯一标识
- `username`: 用户名，唯一索引
- `password`: 密码，明文存储（按需求要求）
- `phone`: 手机号，唯一索引，支持手机号登录
- `email`: 邮箱，唯一索引，支持邮箱登录
- `avatar`: 头像路径，默认"测试图片.jpg"
- `real_name`: 真实姓名
- `gender`: 性别（0-未知，1-男，2-女）
- `age`: 年龄
- `address`: 地址信息
- `status`: 账户状态（0-禁用，1-启用）
- `volunteer_hours`: 累计志愿服务时长，核心字段
- `created_time/updated_time`: 时间戳

### 2. 管理员表 (admins)
**功能**: 存储后台管理员信息
- 独立的管理员表，与普通用户分离
- 支持用户名和手机号登录
- 包含基本信息和状态管理

### 3. 公告表 (announcements)
**功能**: 首页轮播公告管理
- `title`: 公告标题
- `content`: 公告内容
- `cover_image`: 封面图片
- `images`: 轮播图片，JSON格式存储多张图片
- `status`: 上线/下线状态
- `sort_order`: 排序权重
- `view_count`: 查看次数统计
- `admin_id`: 发布管理员关联

### 4. 活动表 (activities)
**功能**: 社区活动管理
- `title/content`: 活动标题和内容
- `cover_image/images`: 封面和多图支持
- `location`: 活动地点
- `max_participants`: 最大参与人数
- `current_participants`: 当前报名人数
- `activity_start_time/activity_end_time`: 活动时间
- `registration_start_time/registration_end_time`: 报名时间
- `status`: 活动状态（0-已取消，1-报名中，2-进行中，3-已结束）

### 5. 志愿服务表 (volunteer_services)
**功能**: 志愿服务管理
- 类似活动表结构
- `service_hours`: 志愿服务时长，核心字段
- `service_start_time/service_end_time`: 服务时间
- `registration_start_time/registration_end_time`: 招募时间
- `status`: 服务状态（0-已取消，1-招募中，2-进行中，3-已结束，4-已确认）

### 6. 活动报名表 (activity_registrations)
**功能**: 活动报名记录
- `user_id/activity_id`: 用户和活动关联
- `status`: 报名状态（0-已取消，1-已报名，2-已参与）
- `registration_time`: 报名时间
- 唯一约束防止重复报名

### 7. 志愿服务报名表 (volunteer_registrations)
**功能**: 志愿服务报名记录
- 类似活动报名表
- `status`: 报名状态（0-已取消，1-已报名，2-已参与，3-已确认）
- `confirmed_time`: 管理员确认时间，用于时长累计

### 8. 用户会话表 (user_sessions)
**功能**: Token会话管理
- `user_id`: 用户ID
- `token`: 会话令牌，唯一索引
- `user_type`: 用户类型（1-普通用户，2-管理员）
- `expires_at`: 过期时间
- 支持自动登录和会话管理

### 9. 系统统计表 (system_statistics)
**功能**: 数据统计和仪表盘
- `stat_date`: 统计日期，唯一索引
- `total_users/total_activities/total_services`: 总数统计
- `daily_registrations`: 当日报名数
- `daily_volunteer_hours`: 当日志愿时长

## 核心功能支持

### 用户端功能
1. **认证授权**: users表 + user_sessions表
2. **首页**: announcements表（轮播公告）
3. **活动中心**: activities表 + activity_registrations表
4. **志愿中心**: volunteer_services表 + volunteer_registrations表
5. **个人中心**: 
   - 我的报名：两个报名表联合查询
   - 我的时长：users.volunteer_hours + volunteer_registrations确认记录

### 管理员端功能
1. **后台登录**: admins表 + user_sessions表
2. **仪表盘**: system_statistics表
3. **内容管理**: announcements、activities、volunteer_services表
4. **数据统计**: 各表统计查询
5. **居民管理**: users表CRUD操作

## 测试数据说明

### 管理员账户
- admin/123456 (系统管理员)
- manager/123456 (社区管理员)

### 测试用户
- zhangsan/123456 (志愿时长15.5小时)
- lisi/123456 (志愿时长22.0小时)
- wangwu/123456 (志愿时长8.5小时)
- zhaoliu/123456 (志愿时长30.0小时)
- sunqi/123456 (志愿时长12.0小时)

### 测试内容
- 3条公告（春节活动、志愿招募、环境整治）
- 3个活动（春节联欢会、亲子运动会、健康讲座）
- 4个志愿服务（环境清洁、老年陪护、儿童教育、图书整理）
- 完整的报名记录和统计数据

## 索引优化
创建了关键字段的索引以优化查询性能：
- 用户表：手机号、邮箱、状态
- 活动/服务表：状态、时间范围
- 报名表：用户ID、活动/服务ID
- 会话表：令牌、过期时间

## 图片存储
- 所有图片路径指向：D:\ccsmSys_Pic\
- 测试图片统一使用：测试图片.jpg
- 多图片使用JSON格式存储：["图片1.jpg","图片2.jpg"]

## 数据完整性
- 外键约束确保数据一致性
- 唯一约束防止重复数据
- 状态字段规范化管理
- 时间戳自动维护

此数据库设计完全支持项目所有功能需求，可直接用于开发和测试。
