-- 社区便民服务管理系统数据库设计
-- 创建数据库
DROP DATABASE IF EXISTS ccsm_system;
CREATE DATABASE ccsm_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE ccsm_system;

-- 1. 用户表
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码(明文)',
    phone VARCHAR(20) UNIQUE COMMENT '手机号',
    email VARCHAR(100) UNIQUE COMMENT '邮箱',
    avatar VARCHAR(255) COMMENT '头像路径',
    real_name VARCHAR(50) COMMENT '真实姓名',
    gender TINYINT DEFAULT 0 COMMENT '性别：0-未知，1-男，2-女',
    age INT COMMENT '年龄',
    address VARCHAR(255) COMMENT '地址',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    volunteer_hours DECIMAL(10,2) DEFAULT 0.00 COMMENT '累计志愿服务时长',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT '用户表';

-- 2. 管理员表
CREATE TABLE admins (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '管理员ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '管理员用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码(明文)',
    phone VARCHAR(20) UNIQUE COMMENT '手机号',
    email VARCHAR(100) COMMENT '邮箱',
    real_name VARCHAR(50) COMMENT '真实姓名',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT '管理员表';

-- 3. 公告表
CREATE TABLE announcements (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '公告ID',
    title VARCHAR(200) NOT NULL COMMENT '公告标题',
    content TEXT COMMENT '公告内容',
    cover_image VARCHAR(255) COMMENT '封面图片',
    images TEXT COMMENT '轮播图片(JSON格式存储)',
    status TINYINT DEFAULT 1 COMMENT '状态：0-下线，1-上线',
    sort_order INT DEFAULT 0 COMMENT '排序权重',
    view_count INT DEFAULT 0 COMMENT '查看次数',
    is_hot TINYINT DEFAULT 0 COMMENT '是否热门：0-否，1-是',
    admin_id BIGINT COMMENT '发布管理员ID',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (admin_id) REFERENCES admins(id)
) COMMENT '公告表';

-- 4. 活动表
CREATE TABLE activities (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '活动ID',
    title VARCHAR(200) NOT NULL COMMENT '活动标题',
    content TEXT COMMENT '活动内容',
    cover_image VARCHAR(255) COMMENT '封面图片',
    images TEXT COMMENT '活动图片(JSON格式存储)',
    location VARCHAR(255) COMMENT '活动地点',
    max_participants INT DEFAULT 0 COMMENT '最大参与人数',
    current_participants INT DEFAULT 0 COMMENT '当前报名人数',
    activity_start_time DATETIME COMMENT '活动开始时间',
    activity_end_time DATETIME COMMENT '活动结束时间',
    registration_start_time DATETIME COMMENT '报名开始时间',
    registration_end_time DATETIME COMMENT '报名结束时间',
    status TINYINT DEFAULT 1 COMMENT '状态：0-已取消，1-报名中，2-进行中，3-已结束',
    is_hot TINYINT DEFAULT 0 COMMENT '是否热门：0-否，1-是',
    admin_id BIGINT COMMENT '发布管理员ID',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (admin_id) REFERENCES admins(id)
) COMMENT '活动表';

-- 5. 志愿服务表
CREATE TABLE volunteer_services (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '志愿服务ID',
    title VARCHAR(200) NOT NULL COMMENT '服务标题',
    content TEXT COMMENT '服务内容',
    cover_image VARCHAR(255) COMMENT '封面图片',
    images TEXT COMMENT '服务图片(JSON格式存储)',
    location VARCHAR(255) COMMENT '服务地点',
    max_participants INT DEFAULT 0 COMMENT '最大参与人数',
    current_participants INT DEFAULT 0 COMMENT '当前报名人数',
    service_hours DECIMAL(10,2) DEFAULT 0.00 COMMENT '志愿服务时长',
    service_start_time DATETIME COMMENT '服务开始时间',
    service_end_time DATETIME COMMENT '服务结束时间',
    registration_start_time DATETIME COMMENT '招募开始时间',
    registration_end_time DATETIME COMMENT '招募结束时间',
    status TINYINT DEFAULT 1 COMMENT '状态：0-已取消，1-招募中，2-进行中，3-已结束，4-已确认',
    is_hot TINYINT DEFAULT 0 COMMENT '是否热门：0-否，1-是',
    admin_id BIGINT COMMENT '发布管理员ID',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (admin_id) REFERENCES admins(id)
) COMMENT '志愿服务表';

-- 6. 活动报名表
CREATE TABLE activity_registrations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '报名ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    activity_id BIGINT NOT NULL COMMENT '活动ID',
    status TINYINT DEFAULT 1 COMMENT '状态：0-已取消，1-已报名，2-已参与',
    registration_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '报名时间',
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (activity_id) REFERENCES activities(id),
    UNIQUE KEY uk_user_activity (user_id, activity_id)
) COMMENT '活动报名表';

-- 7. 志愿服务报名表
CREATE TABLE volunteer_registrations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '报名ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    service_id BIGINT NOT NULL COMMENT '志愿服务ID',
    status TINYINT DEFAULT 1 COMMENT '状态：0-已取消，1-已报名，2-已参与，3-已确认',
    registration_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '报名时间',
    confirmed_time DATETIME COMMENT '确认时间',
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (service_id) REFERENCES volunteer_services(id),
    UNIQUE KEY uk_user_service (user_id, service_id)
) COMMENT '志愿服务报名表';

-- 8. 用户会话表
CREATE TABLE user_sessions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '会话ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    token VARCHAR(255) NOT NULL UNIQUE COMMENT '会话令牌',
    user_type TINYINT DEFAULT 1 COMMENT '用户类型：1-普通用户，2-管理员',
    expires_at DATETIME NOT NULL COMMENT '过期时间',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (user_id) REFERENCES users(id)
) COMMENT '用户会话表';

-- 9. 验证码表
CREATE TABLE verification_codes (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '验证码ID',
    phone VARCHAR(20) COMMENT '手机号',
    email VARCHAR(100) COMMENT '邮箱',
    code VARCHAR(10) NOT NULL COMMENT '验证码',
    type TINYINT NOT NULL COMMENT '类型：1-注册，2-忘记密码',
    status TINYINT DEFAULT 0 COMMENT '状态：0-未使用，1-已使用，2-已过期',
    expires_at DATETIME NOT NULL COMMENT '过期时间',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) COMMENT '验证码表';

-- 10. 系统统计表
CREATE TABLE system_statistics (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '统计ID',
    stat_date DATE NOT NULL COMMENT '统计日期',
    total_users INT DEFAULT 0 COMMENT '总用户数',
    total_activities INT DEFAULT 0 COMMENT '总活动数',
    total_services INT DEFAULT 0 COMMENT '总志愿服务数',
    daily_registrations INT DEFAULT 0 COMMENT '当日报名数',
    daily_volunteer_hours DECIMAL(10,2) DEFAULT 0.00 COMMENT '当日志愿时长',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE KEY uk_stat_date (stat_date)
) COMMENT '系统统计表';

-- ========================================
-- 初始测试数据插入
-- ========================================

-- 插入管理员数据
INSERT INTO admins (username, password, phone, email, real_name) VALUES
('admin', '123456', '13800000001', '<EMAIL>', '系统管理员'),
('manager', '123456', '13800000002', '<EMAIL>', '社区管理员');

-- 插入测试用户数据
INSERT INTO users (username, password, phone, email, real_name, gender, age, address, avatar, volunteer_hours) VALUES
('zhangsan', '123456', '13800000101', '<EMAIL>', '张三', 1, 28, '北京市朝阳区xxx小区1号楼', '测试图片.jpg', 15.5),
('lisi', '123456', '13800000102', '<EMAIL>', '李四', 2, 32, '北京市朝阳区xxx小区2号楼', '测试图片.jpg', 22.0),
('wangwu', '123456', '13800000103', '<EMAIL>', '王五', 1, 25, '北京市朝阳区xxx小区3号楼', '测试图片.jpg', 8.5),
('zhaoliu', '123456', '13800000104', '<EMAIL>', '赵六', 2, 35, '北京市朝阳区xxx小区4号楼', '测试图片.jpg', 30.0),
('sunqi', '123456', '13800000105', '<EMAIL>', '孙七', 1, 29, '北京市朝阳区xxx小区5号楼', '测试图片.jpg', 12.0);

-- 插入公告数据
INSERT INTO announcements (title, content, cover_image, images, admin_id, view_count, is_hot) VALUES
('社区春节活动通知', '亲爱的居民朋友们，春节即将到来，社区将举办丰富多彩的春节庆祝活动，欢迎大家积极参与！', '测试图片.jpg', '["测试图片.jpg","测试图片.jpg"]', 1, 156, 1),
('志愿服务招募公告', '为了更好地服务社区居民，现面向全体居民招募志愿者，共同建设美好社区！', '测试图片.jpg', '["测试图片.jpg"]', 1, 89, 0),
('社区环境整治活动', '为营造整洁优美的社区环境，特组织环境整治活动，期待您的参与！', '测试图片.jpg', '["测试图片.jpg","测试图片.jpg","测试图片.jpg"]', 2, 234, 1);

-- 插入活动数据
INSERT INTO activities (title, content, cover_image, images, location, max_participants, current_participants,
                       activity_start_time, activity_end_time, registration_start_time, registration_end_time,
                       status, is_hot, admin_id) VALUES
('社区春节联欢会', '欢度春节，共庆佳节！社区将举办盛大的春节联欢会，有精彩的文艺表演、游戏互动和丰富奖品等您来拿！',
 '测试图片.jpg', '["测试图片.jpg","测试图片.jpg"]', '社区活动中心', 200, 45,
 '2024-02-10 19:00:00', '2024-02-10 21:30:00', '2024-01-20 09:00:00', '2024-02-08 18:00:00', 1, 1, 1),

('亲子运动会', '增进亲子关系，锻炼身体素质！社区亲子运动会即将开始，设有多个趣味项目，适合全家参与！',
 '测试图片.jpg', '["测试图片.jpg"]', '社区运动场', 100, 28,
 '2024-03-15 14:00:00', '2024-03-15 17:00:00', '2024-03-01 09:00:00', '2024-03-13 18:00:00', 1, 1, 1),

('健康知识讲座', '关爱健康，科学养生！邀请专业医生为大家讲解日常保健知识和疾病预防方法。',
 '测试图片.jpg', '["测试图片.jpg","测试图片.jpg"]', '社区会议室', 80, 67,
 '2024-02-25 15:00:00', '2024-02-25 16:30:00', '2024-02-15 09:00:00', '2024-02-23 18:00:00', 2, 0, 2);

-- 插入志愿服务数据
INSERT INTO volunteer_services (title, content, cover_image, images, location, max_participants, current_participants,
                                service_hours, service_start_time, service_end_time, registration_start_time,
                                registration_end_time, status, is_hot, admin_id) VALUES
('社区环境清洁志愿服务', '美化社区环境，从我做起！组织志愿者清理社区卫生死角，维护整洁环境。',
 '测试图片.jpg', '["测试图片.jpg"]', '社区各区域', 30, 15, 3.0,
 '2024-02-20 08:00:00', '2024-02-20 11:00:00', '2024-02-10 09:00:00', '2024-02-18 18:00:00', 1, 1, 1),

('老年人陪护志愿服务', '关爱老年人，传递温暖！为社区独居老人提供陪护服务，聊天谈心，帮助解决生活困难。',
 '测试图片.jpg', '["测试图片.jpg","测试图片.jpg"]', '社区老年活动中心', 20, 12, 4.0,
 '2024-03-05 14:00:00', '2024-03-05 18:00:00', '2024-02-20 09:00:00', '2024-03-03 18:00:00', 1, 1, 2),

('儿童安全教育志愿服务', '守护儿童安全，共建平安社区！为社区儿童开展安全知识教育，提高自我保护意识。',
 '测试图片.jpg', '["测试图片.jpg"]', '社区儿童活动室', 15, 8, 2.5,
 '2024-02-28 15:00:00', '2024-02-28 17:30:00', '2024-02-15 09:00:00', '2024-02-26 18:00:00', 4, 0, 1),

('社区图书整理志愿服务', '整理图书，传播知识！帮助社区图书室整理图书，为居民提供更好的阅读环境。',
 '测试图片.jpg', '["测试图片.jpg","测试图片.jpg"]', '社区图书室', 10, 6, 3.5,
 '2024-01-25 09:00:00', '2024-01-25 12:30:00', '2024-01-15 09:00:00', '2024-01-23 18:00:00', 4, 0, 2);

-- 插入活动报名数据
INSERT INTO activity_registrations (user_id, activity_id, status) VALUES
(1, 1, 1), (2, 1, 1), (3, 1, 1), (4, 1, 1), (5, 1, 1),
(1, 2, 1), (2, 2, 1), (3, 2, 1),
(1, 3, 2), (2, 3, 2), (3, 3, 2), (4, 3, 2), (5, 3, 2);

-- 插入志愿服务报名数据
INSERT INTO volunteer_registrations (user_id, service_id, status, confirmed_time) VALUES
(1, 1, 1, NULL), (2, 1, 1, NULL), (3, 1, 1, NULL),
(1, 2, 1, NULL), (4, 2, 1, NULL),
(2, 3, 3, '2024-02-28 18:00:00'), (3, 3, 3, '2024-02-28 18:00:00'), (5, 3, 3, '2024-02-28 18:00:00'),
(1, 4, 3, '2024-01-25 13:00:00'), (2, 4, 3, '2024-01-25 13:00:00'), (4, 4, 3, '2024-01-25 13:00:00');

-- 插入验证码测试数据
INSERT INTO verification_codes (phone, email, code, type, status, expires_at) VALUES
('13800000106', NULL, '123456', 1, 0, DATE_ADD(NOW(), INTERVAL 10 MINUTE)),
(NULL, '<EMAIL>', '654321', 2, 0, DATE_ADD(NOW(), INTERVAL 10 MINUTE)),
('13800000107', NULL, '111111', 1, 1, DATE_ADD(NOW(), INTERVAL -5 MINUTE));

-- 插入系统统计数据
INSERT INTO system_statistics (stat_date, total_users, total_activities, total_services, daily_registrations, daily_volunteer_hours) VALUES
('2024-01-25', 5, 3, 4, 8, 10.5),
('2024-02-15', 5, 3, 4, 12, 7.5),
('2024-02-20', 5, 3, 4, 6, 0.0),
('2024-02-28', 5, 3, 4, 3, 7.5);

-- 更新活动当前参与人数
UPDATE activities SET current_participants = (
    SELECT COUNT(*) FROM activity_registrations
    WHERE activity_registrations.activity_id = activities.id AND status IN (1,2)
);

-- 更新志愿服务当前参与人数
UPDATE volunteer_services SET current_participants = (
    SELECT COUNT(*) FROM volunteer_registrations
    WHERE volunteer_registrations.service_id = volunteer_services.id AND status IN (1,2,3)
);

-- 创建索引优化查询性能
CREATE INDEX idx_users_phone ON users(phone);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_announcements_status ON announcements(status);
CREATE INDEX idx_announcements_hot ON announcements(is_hot);
CREATE INDEX idx_activities_status ON activities(status);
CREATE INDEX idx_activities_hot ON activities(is_hot);
CREATE INDEX idx_activities_time ON activities(activity_start_time, activity_end_time);
CREATE INDEX idx_volunteer_services_status ON volunteer_services(status);
CREATE INDEX idx_volunteer_services_hot ON volunteer_services(is_hot);
CREATE INDEX idx_volunteer_services_time ON volunteer_services(service_start_time, service_end_time);
CREATE INDEX idx_activity_registrations_user ON activity_registrations(user_id);
CREATE INDEX idx_activity_registrations_activity ON activity_registrations(activity_id);
CREATE INDEX idx_volunteer_registrations_user ON volunteer_registrations(user_id);
CREATE INDEX idx_volunteer_registrations_service ON volunteer_registrations(service_id);
CREATE INDEX idx_volunteer_registrations_status ON volunteer_registrations(status);
CREATE INDEX idx_user_sessions_token ON user_sessions(token);
CREATE INDEX idx_user_sessions_expires ON user_sessions(expires_at);
CREATE INDEX idx_verification_codes_phone ON verification_codes(phone);
CREATE INDEX idx_verification_codes_email ON verification_codes(email);
CREATE INDEX idx_verification_codes_expires ON verification_codes(expires_at);

-- 数据库设计完成
-- 总计10张核心表：users, admins, announcements, activities, volunteer_services,
-- activity_registrations, volunteer_registrations, user_sessions, verification_codes, system_statistics
-- 包含完整的测试数据，支持所有功能模块的开发和测试

-- 功能支持完整性检查：
-- ✅ 用户注册/登录/忘记密码（手机号/邮箱 + 验证码）
-- ✅ Token会话管理（自动登录/退出）
-- ✅ 首页公告轮播图（支持热门标识）
-- ✅ 活动中心（列表、详情、报名、状态筛选、热门预览）
-- ✅ 志愿中心（列表、详情、报名、状态筛选、热门预览）
-- ✅ 个人中心（我的报名、我的时长、修改密码）
-- ✅ 管理员后台登录
-- ✅ 仪表盘数据统计
-- ✅ 内容管理（公告、活动、服务的增删改查）
-- ✅ 志愿服务确认和时长累计
-- ✅ 居民管理（用户CRUD、状态管理）
-- ✅ 数据统计（活动统计、志愿统计）
